"use strict";
const electron = require("electron");
const path = require("path");
const utils = require("@electron-toolkit/utils");
const sqlite3 = require("sqlite3");
const fs = require("fs");
const promises = require("fs/promises");
const sharp = require("sharp");
const icon = path.join(__dirname, "../../resources/icon.png");
let mainWindow = null;
const dbPath = path.join(electron.app.getPath("userData"), "photos.db");
const db = new sqlite3.Database(dbPath);
const thumbnailsDir = path.join(electron.app.getPath("userData"), "thumbnails");
if (!fs.existsSync(thumbnailsDir)) {
  fs.mkdirSync(thumbnailsDir, { recursive: true });
}
const createTables = () => {
  db.serialize(() => {
    db.run("PRAGMA foreign_keys = ON");
    db.run(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT
      )
    `);
    db.run(`
      CREATE TABLE IF NOT EXISTS photos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        path TEXT UNIQUE,
        date TEXT,
        thumbnail_path TEXT
      )
    `);
    db.run(`
      CREATE TABLE IF NOT EXISTS tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE
      )
    `);
    db.run(`
      CREATE TABLE IF NOT EXISTS photo_tags (
        photo_id INTEGER,
        tag_id INTEGER,
        PRIMARY KEY (photo_id, tag_id),
        FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
      )
    `);
  });
};
function createWindow() {
  mainWindow = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    autoHideMenuBar: true,
    ...process.platform === "linux" ? { icon } : {},
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      webSecurity: false,
      // Temporarily disable for file access
      contextIsolation: true,
      nodeIntegration: false
    }
  });
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        "Content-Security-Policy": [
          "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: thumbnail: photo:; connect-src 'self' ws: wss:"
        ]
      }
    });
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
electron.protocol.registerSchemesAsPrivileged([
  {
    scheme: "thumbnail",
    privileges: {
      secure: true,
      standard: true,
      supportsFetchAPI: true,
      corsEnabled: true
    }
  },
  {
    scheme: "photo",
    privileges: {
      secure: true,
      standard: true,
      supportsFetchAPI: true,
      corsEnabled: true
    }
  }
]);
electron.app.whenReady().then(async () => {
  electron.protocol.registerFileProtocol("thumbnail", (request, callback) => {
    const url = request.url.substr(12);
    const filePath = path.join(thumbnailsDir, url);
    console.log("Thumbnail request:", request.url);
    console.log("Serving thumbnail at:", filePath);
    console.log("File exists:", fs.existsSync(filePath));
    callback({ path: filePath });
  });
  electron.protocol.registerFileProtocol("photo", (request, callback) => {
    const url = request.url.substr(8);
    let filePath = decodeURIComponent(url);
    if (process.platform === "win32" && filePath.startsWith("/")) {
      filePath = filePath.substr(1);
    }
    console.log("Photo request:", request.url);
    console.log("Serving photo at:", filePath);
    console.log("File exists:", fs.existsSync(filePath));
    callback({ path: filePath });
  });
  createTables();
  utils.electronApp.setAppUserModelId("com.electron");
  electron.app.on("browser-window-created", (_, window) => {
    utils.optimizer.watchWindowShortcuts(window);
  });
  electron.ipcMain.on("ping", () => console.log("pong"));
  electron.ipcMain.handle("select-folder", async () => {
    const result = await electron.dialog.showOpenDialog({
      properties: ["openDirectory"]
    });
    if (result.canceled) return null;
    const folderPath = result.filePaths[0];
    db.run("INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)", ["selected_folder", folderPath]);
    await new Promise((resolve, reject) => {
      db.run("DELETE FROM photos WHERE path NOT LIKE ?", [`${folderPath}%`], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    await scanFolderWithoutThumbnails(folderPath);
    setTimeout(() => {
      generateMissingThumbnails(folderPath);
    }, 100);
    return folderPath;
  });
  electron.ipcMain.handle(
    "get-photos",
    async (_event, { offset, limit, tags, folderPath }) => {
      let query = "SELECT id, path, date, thumbnail_path FROM photos";
      const params = [];
      if (folderPath) {
        query += " WHERE path LIKE ?";
        params.push(`${folderPath}%`);
      }
      if (tags && tags.length > 0) {
        query += (folderPath ? " AND" : " WHERE") + " photos.id IN (SELECT photo_id FROM photo_tags JOIN tags ON photo_tags.tag_id = tags.id WHERE tags.name IN (" + tags.map(() => "?").join(",") + ") GROUP BY photo_id HAVING COUNT(*) = ?)";
        params.push(...tags, tags.length);
      }
      query += " ORDER BY date DESC LIMIT ? OFFSET ?";
      params.push(limit, offset);
      return new Promise((resolve, reject) => {
        db.all(query, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });
    }
  );
  electron.ipcMain.handle("get-folder-tree", async (_event, folderPath) => {
    if (!folderPath) return null;
    try {
      return await buildFolderTree(folderPath);
    } catch (error) {
      console.error("Error getting folder tree:", error);
      return null;
    }
  });
  electron.ipcMain.handle("get-photo-tags", async (_event, photoId) => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT tags.name FROM tags 
         JOIN photo_tags ON tags.id = photo_tags.tag_id 
         WHERE photo_tags.photo_id = ?`,
        [photoId],
        (err, rows) => {
          if (err) {
            console.error("Error getting photo tags:", err);
            reject(err);
          } else {
            resolve(rows.map((row) => row.name));
          }
        }
      );
    });
  });
  electron.ipcMain.handle("get-all-tags", async () => {
    return new Promise((resolve, reject) => {
      db.all("SELECT name FROM tags ORDER BY name", [], (err, rows) => {
        if (err) {
          console.error("Error getting all tags:", err);
          reject(err);
        } else {
          resolve(rows.map((row) => row.name));
        }
      });
    });
  });
  electron.ipcMain.handle("add-tag-to-photo", async (_event, { photoId, tagName }) => {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        db.run("BEGIN TRANSACTION");
        db.run("INSERT OR IGNORE INTO tags (name) VALUES (?)", [tagName], function(err) {
          if (err) {
            db.run("ROLLBACK");
            console.error("Error adding tag:", err);
            reject(err);
            return;
          }
          db.get("SELECT id FROM tags WHERE name = ?", [tagName], (err2, row) => {
            if (err2 || !row) {
              db.run("ROLLBACK");
              console.error("Error getting tag ID:", err2);
              reject(err2 || new Error("Tag not found"));
              return;
            }
            db.run(
              "INSERT OR IGNORE INTO photo_tags (photo_id, tag_id) VALUES (?, ?)",
              [photoId, row.id],
              (err3) => {
                if (err3) {
                  db.run("ROLLBACK");
                  console.error("Error linking tag to photo:", err3);
                  reject(err3);
                } else {
                  db.run("COMMIT");
                  resolve();
                }
              }
            );
          });
        });
      });
    });
  });
  electron.ipcMain.handle("remove-tag-from-photo", async (_event, { photoId, tagName }) => {
    return new Promise((resolve, reject) => {
      db.run(
        `DELETE FROM photo_tags
         WHERE photo_id = ? AND tag_id = (SELECT id FROM tags WHERE name = ?)`,
        [photoId, tagName],
        (err) => {
          if (err) {
            console.error("Error removing tag from photo:", err);
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  });
  electron.ipcMain.handle("clear-all-thumbnails", async () => {
    try {
      await new Promise((resolve, reject) => {
        db.run("UPDATE photos SET thumbnail_path = NULL", [], (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      console.log("Cleared all thumbnail paths from database");
      return { success: true, message: "All thumbnail paths cleared from database" };
    } catch (error) {
      console.error("Error clearing thumbnail paths:", error);
      return { success: false, message: "Failed to clear thumbnail paths" };
    }
  });
  const cleanupThumbnails = async () => {
    try {
      const dbThumbnails = await new Promise((resolve, reject) => {
        db.all("SELECT thumbnail_path FROM photos WHERE thumbnail_path IS NOT NULL", [], (err, rows) => {
          if (err) reject(err);
          else resolve(new Set(rows.map((row) => row.thumbnail_path)));
        });
      });
      const files = await promises.readdir(thumbnailsDir);
      for (const file of files) {
        if (!dbThumbnails.has(file)) {
          try {
            await promises.unlink(path.join(thumbnailsDir, file));
            console.log("Deleted orphaned thumbnail:", file);
          } catch (error) {
            console.error("Error deleting thumbnail:", file, error);
          }
        }
      }
    } catch (error) {
      console.error("Error cleaning up thumbnails:", error);
    }
  };
  await cleanupThumbnails();
  createWindow();
  electron.app.on("activate", () => {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
  electron.app.on("will-quit", () => {
    db.close((err) => {
      if (err) console.error("Error closing database:", err.message);
    });
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
const buildFolderTree = async (rootPath) => {
  try {
    const entries = await promises.readdir(rootPath, { withFileTypes: true });
    const nodes = [];
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const fullPath = path.join(rootPath, entry.name);
        try {
          const children = await buildFolderTree(fullPath);
          nodes.push({
            name: entry.name,
            path: fullPath,
            isDirectory: true,
            children
          });
        } catch (error) {
          console.error(`Error processing directory ${fullPath}:`, error);
        }
      }
    }
    return nodes.sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.error(`Error reading directory ${rootPath}:`, error);
    return [];
  }
};
const scanFolderWithoutThumbnails = async (folderPath) => {
  try {
    const existingPaths = await getExistingPaths();
    const files = await promises.readdir(folderPath);
    for (const file of files) {
      const fullPath = path.join(folderPath, file);
      try {
        const stat = await promises.stat(fullPath);
        if (stat.isDirectory()) {
          await scanFolderWithoutThumbnails(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(file).toLowerCase();
          if (imageExtensions.includes(ext)) {
            if (!existingPaths.has(fullPath)) {
              const mtime = stat.mtime.toISOString();
              try {
                console.log(`Inserting new photo: ${fullPath}`);
                await insertPhotoWithoutThumbnail(fullPath, mtime);
              } catch (error) {
                console.error(`Error inserting photo ${fullPath}:`, error);
              }
            }
          }
        }
      } catch (error) {
        console.error(`Error processing ${fullPath}:`, error);
      }
    }
  } catch (error) {
    console.error(`Error scanning folder ${folderPath}:`, error);
    throw error;
  }
};
const generateMissingThumbnails = async (folderPath) => {
  try {
    console.log("Starting background thumbnail generation...");
    let query = "SELECT id, path FROM photos WHERE thumbnail_path IS NULL";
    const params = [];
    if (folderPath) {
      query += " AND path LIKE ?";
      params.push(`${folderPath}%`);
    }
    query += " ORDER BY date DESC";
    const photosWithoutThumbnails = await new Promise((resolve, reject) => {
      db.all(query, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    console.log(`Found ${photosWithoutThumbnails.length} photos without thumbnails`);
    for (const photo of photosWithoutThumbnails) {
      try {
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send("thumbnail-generation-started", {
            photoId: photo.id,
            photoPath: photo.path
          });
        }
        const thumbnailFilename = await generateThumbnail(photo.path, photo.id);
        if (thumbnailFilename) {
          await new Promise((resolve, reject) => {
            db.run("UPDATE photos SET thumbnail_path = ? WHERE id = ?", [thumbnailFilename, photo.id], (err) => {
              if (err) reject(err);
              else resolve();
            });
          });
          console.log(`Updated photo ${photo.id} with thumbnail: ${thumbnailFilename}`);
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send("thumbnail-generated", {
              photoId: photo.id,
              thumbnailPath: thumbnailFilename
            });
          }
        }
      } catch (error) {
        console.error(`Error generating thumbnail for photo ${photo.id}:`, error);
      }
    }
    console.log("Background thumbnail generation completed");
  } catch (error) {
    console.error("Error in generateMissingThumbnails:", error);
  }
};
const getExistingPaths = () => {
  return new Promise((resolve, reject) => {
    db.all("SELECT path FROM photos", [], (err, rows) => {
      if (err) reject(err);
      else resolve(new Set(rows.map((row) => row.path)));
    });
  });
};
const insertPhotoWithoutThumbnail = (photoPath, date) => {
  return new Promise((resolve, reject) => {
    db.run("INSERT INTO photos (path, date) VALUES (?, ?)", [photoPath, date], function(err) {
      if (err) reject(err);
      else resolve(this.lastID);
    });
  });
};
const generateThumbnail = async (photoPath, id) => {
  try {
    console.log(`Starting thumbnail generation for: ${photoPath}`);
    const normalizedPath = path.normalize(photoPath);
    if (normalizedPath.includes("..")) {
      throw new Error("Invalid file path");
    }
    const filename = `${id}_thumb.jpg`;
    const thumbnailPath = path.join(thumbnailsDir, filename);
    console.log(`Thumbnail will be saved to: ${thumbnailPath}`);
    await sharp(photoPath).resize(200, 200, { fit: "inside", withoutEnlargement: true }).toFormat("jpg").toFile(thumbnailPath);
    console.log(`Thumbnail generated successfully: ${filename}`);
    return filename;
  } catch (err) {
    console.error("Error generating thumbnail for", photoPath, err);
    try {
      const filename = `${id}_thumb_placeholder.jpg`;
      const placeholderPath = path.join(thumbnailsDir, filename);
      await sharp({
        create: {
          width: 200,
          height: 200,
          channels: 3,
          background: { r: 200, g: 200, b: 200 }
        }
      }).jpeg().toFile(placeholderPath);
      return filename;
    } catch (placeholderErr) {
      console.error("Error creating placeholder thumbnail:", placeholderErr);
      return void 0;
    }
  }
};
