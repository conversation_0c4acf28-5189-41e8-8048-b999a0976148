import React, { useState } from 'react';

interface FolderNode {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FolderNode[];
}

interface FolderTreeProps {
  node: FolderNode;
  onSelect: (path: string) => void;
  isRoot?: boolean;
  depth?: number;
}

const FolderTree: React.FC<FolderTreeProps> = ({ node, onSelect, isRoot = false, depth = 0 }) => {
  const [expanded, setExpanded] = useState(isRoot);

  const handleToggle = (e: React.MouseEvent): void => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  const handleSelect = (): void => {
    onSelect(node.path);
  };

  const hasChildren = node.children && node.children.length > 0;

  return (
    <div className="select-none">
      <div
        className={`
          flex items-center py-2 px-3 rounded-lg cursor-pointer transition-all duration-200
          hover:bg-slate-100 hover:shadow-sm group
          ${depth > 0 ? 'ml-4' : ''}
        `}
        style={{ paddingLeft: `${depth * 16 + 12}px` }}
        onClick={handleSelect}
      >
        {hasChildren ? (
          <button
            className="mr-2 w-5 h-5 flex items-center justify-center rounded hover:bg-slate-200 transition-colors"
            onClick={handleToggle}
          >
            <svg
              className={`w-3 h-3 text-slate-600 transition-transform duration-200 ${expanded ? 'rotate-90' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        ) : (
          <div className="mr-2 w-5 h-5"></div>
        )}

        <div className="flex items-center flex-1 min-w-0">
          <svg className="w-4 h-4 mr-2 text-slate-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
          </svg>
          <span className="text-sm text-slate-700 truncate group-hover:text-slate-900 transition-colors">
            {node.name}
          </span>
        </div>
      </div>

      {expanded && hasChildren && (
        <div className="mt-1">
          {node.children!.map((childNode, index) => (
            <FolderTree
              key={index}
              node={childNode}
              onSelect={onSelect}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FolderTree;