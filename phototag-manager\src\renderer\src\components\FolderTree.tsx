import React, { useState, useEffect } from 'react';

interface FolderNode {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FolderNode[];
}

interface FolderTreeProps {
  node: FolderNode;
  onSelect: (path: string) => void;
  selectedPath?: string;
  isRoot?: boolean;
  depth?: number;
}

const FolderTree: React.FC<FolderTreeProps> = ({
  node,
  onSelect,
  selectedPath,
  isRoot = false,
  depth = 0
}) => {
  const [expanded, setExpanded] = useState(isRoot);
  const hasChildren = node.children && node.children.length > 0;
  const isSelected = selectedPath === node.path;
  const isParentOfSelected = selectedPath?.startsWith(node.path + '\\') || selectedPath?.startsWith(node.path + '/');

  // Auto-expand if this folder is a parent of the selected path
  useEffect(() => {
    if (isParentOfSelected || isSelected) {
      setExpanded(true);
    }
  }, [selectedPath, isParentOfSelected, isSelected]);

  const handleToggle = (e: React.MouseEvent): void => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  const handleSelect = (): void => {
    onSelect(node.path);
  };

  return (
    <div className="select-none">
      <div
        className={`
          flex items-center py-1.5 px-2 rounded transition-all duration-200 cursor-pointer
          ${isSelected
            ? 'bg-blue-100 text-blue-900 border border-blue-200'
            : 'hover:bg-slate-100 text-slate-700'
          }
        `}
        style={{ paddingLeft: `${depth * 20 + 8}px` }}
        onClick={handleSelect}
      >
        {hasChildren && (
          <div
            className="mr-1 w-4 h-4 flex items-center justify-center hover:bg-slate-200 rounded transition-colors"
            onClick={handleToggle}
          >
            <svg
              className={`w-3 h-3 text-slate-500 transition-transform duration-200 ${expanded ? 'rotate-90' : ''}`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        )}

        <svg
          className={`w-4 h-4 mr-2 flex-shrink-0 ${isSelected ? 'text-blue-600' : 'text-slate-500'}`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
        </svg>

        <span className={`text-sm truncate ${isSelected ? 'font-medium' : ''}`}>
          {node.name}
        </span>
      </div>

      {expanded && hasChildren && (
        <div>
          {node.children!.map((childNode, index) => (
            <FolderTree
              key={index}
              node={childNode}
              onSelect={onSelect}
              selectedPath={selectedPath}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FolderTree;