import React, { useState } from 'react';

interface FolderNode {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FolderNode[];
}

interface FolderTreeProps {
  node: FolderNode;
  onSelect: (path: string) => void;
  isRoot?: boolean;
}

const FolderTree: React.FC<FolderTreeProps> = ({ node, onSelect, isRoot = false }) => {
  const [expanded, setExpanded] = useState(isRoot);

  const handleToggle = (e: React.MouseEvent): void => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  const handleSelect = (): void => {
    onSelect(node.path);
  };

  return (
    <div className="select-none">
      <div
        className="flex items-center py-1 px-2 hover:bg-gray-100 cursor-pointer"
        onClick={handleSelect}
      >
        {node.children && node.children.length > 0 ? (
          <span
            className="mr-1 text-gray-500 w-4 text-center"
            onClick={handleToggle}
          >
            {expanded ? '▼' : '►'}
          </span>
        ) : (
          <span className="mr-1 w-4"></span>
        )}
        <span className="truncate">{node.name}</span>
      </div>
      
      {expanded && node.children && (
        <div className="ml-4 border-l border-gray-200 pl-2">
          {node.children.map((childNode, index) => (
            <FolderTree
              key={index}
              node={childNode}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FolderTree;