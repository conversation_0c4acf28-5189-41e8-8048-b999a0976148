import React, { useState, useEffect, useMemo, useCallback } from 'react';
import FolderTree from './components/FolderTree';

// Define interfaces with strict TypeScript standards
interface Photo {
  id: number;
  path: string;
  date: string;
  thumbnail_path?: string;
}

interface FolderNode {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FolderNode[];
}

interface ElectronAPI {
  ipcRenderer: {
    invoke(channel: 'select-folder'): Promise<string | null>;
    invoke(
      channel: 'get-photos',
      args: { offset: number; limit: number; tags: string[]; folderPath?: string }
    ): Promise<Photo[]>;
    invoke(channel: 'get-folder-tree', folderPath: string): Promise<FolderNode[] | null>;
    invoke(channel: 'get-photo-tags', photoId: number): Promise<string[]>;
    invoke(channel: 'get-all-tags'): Promise<string[]>;
    invoke(channel: 'add-tag-to-photo', args: { photoId: number; tagName: string }): Promise<void>;
    invoke(channel: 'remove-tag-from-photo', args: { photoId: number; tagName: string }): Promise<void>;
  };
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}



const App: React.FC = () => {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loadingState, setLoadingState] = useState<{
    photos: boolean;
    folders: boolean;
    tags: boolean;
    scanning: boolean;
    loadingMore: boolean;
  }>({
    photos: false,
    folders: false,
    tags: false,
    scanning: false,
    loadingMore: false
  });
  const [error, setError] = useState<string | null>(null);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [folderTree, setFolderTree] = useState<FolderNode[] | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [photoTags, setPhotoTags] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);
  const [searchTags, setSearchTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState<string>('');
  const [isFullscreenViewer, setIsFullscreenViewer] = useState<boolean>(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState<number>(0);
  const [hasMorePhotos, setHasMorePhotos] = useState<boolean>(true);
  const [totalPhotos, setTotalPhotos] = useState<number>(0);




  const selectFolder = async (): Promise<void> => {
    try {
      setLoadingState(prev => ({ ...prev, scanning: true }));
      setError(null);
      
      const folderPath = await window.electron.ipcRenderer.invoke('select-folder');
      if (folderPath) {
        setSelectedFolder(folderPath);
        
        // Load folder tree
        setLoadingState(prev => ({ ...prev, folders: true }));
        const tree = await window.electron.ipcRenderer.invoke('get-folder-tree', folderPath);
        setFolderTree(tree);
        
        // Load photos
        setLoadingState(prev => ({ ...prev, photos: true }));
        await loadPhotos(folderPath, searchTags);
        
        // Load all tags
        setLoadingState(prev => ({ ...prev, tags: true }));
        const tags = await window.electron.ipcRenderer.invoke('get-all-tags');
        setAllTags(tags);
      }
    } catch (err) {
      console.error('Error selecting folder:', err);
      setError('Failed to scan folder. Please try again.');
    } finally {
      setLoadingState({
        photos: false,
        folders: false,
        tags: false,
        scanning: false
      });
    }
  };

  // Load photos with pagination for better performance
  const loadPhotos = useCallback(async (folderPath?: string, tags?: string[], reset: boolean = true): Promise<void> => {
    if (!folderPath && !selectedFolder) return;

    try {
      setLoadingState(prev => ({ ...prev, photos: reset, loadingMore: !reset }));
      setError(null);

      const pathToUse: string = folderPath || selectedFolder!;
      const tagsToUse: string[] = tags || [];
      const currentOffset = reset ? 0 : photos.length;
      const batchSize = 100; // Load 100 photos at a time

      const fetchedPhotos: Photo[] = await window.electron.ipcRenderer.invoke('get-photos', {
        offset: currentOffset,
        limit: batchSize,
        tags: tagsToUse,
        folderPath: pathToUse,
      });

      if (reset) {
        setPhotos(fetchedPhotos);
        setTotalPhotos(fetchedPhotos.length);
      } else {
        setPhotos(prev => [...prev, ...fetchedPhotos]);
      }

      setHasMorePhotos(fetchedPhotos.length === batchSize);
    } catch (err) {
      console.error('Error loading photos:', err);
      setError('Failed to load photos. Please try again.');
    } finally {
      setLoadingState(prev => ({ ...prev, photos: false, loadingMore: false }));
    }
  }, [selectedFolder, photos.length]);

  const handleFolderSelect = async (path: string): Promise<void> => {
    setSelectedFolder(path);
    await loadPhotos(path, searchTags);
  };

  const openPhotoView = async (photo: Photo): Promise<void> => {
    const photoIndex = photos.findIndex(p => p.id === photo.id);
    setCurrentPhotoIndex(photoIndex);
    setSelectedPhoto(photo);
    setIsFullscreenViewer(true);
    const tags: string[] = await window.electron.ipcRenderer.invoke('get-photo-tags', photo.id);
    setPhotoTags(tags);
    setNewTag('');
  };

  const navigatePhoto = (direction: 'prev' | 'next'): void => {
    const newIndex = direction === 'prev'
      ? Math.max(0, currentPhotoIndex - 1)
      : Math.min(photos.length - 1, currentPhotoIndex + 1);

    if (newIndex !== currentPhotoIndex) {
      setCurrentPhotoIndex(newIndex);
      const newPhoto = photos[newIndex];
      setSelectedPhoto(newPhoto);

      // Load tags for the new photo
      window.electron.ipcRenderer.invoke('get-photo-tags', newPhoto.id)
        .then(tags => setPhotoTags(tags))
        .catch(error => {
          console.error('Error loading photo tags:', error);
          setPhotoTags([]);
        });
    }
  };

  const closeFullscreenViewer = (): void => {
    setIsFullscreenViewer(false);
    setSelectedPhoto(null);
    setPhotoTags([]);
  };

  const addTag = async (): Promise<void> => {
    if (newTag.trim() && selectedPhoto) {
      await window.electron.ipcRenderer.invoke('add-tag-to-photo', { photoId: selectedPhoto.id, tagName: newTag.trim() });
      const updatedTags: string[] = await window.electron.ipcRenderer.invoke('get-photo-tags', selectedPhoto.id);
      setPhotoTags(updatedTags);
      const allUpdatedTags: string[] = await window.electron.ipcRenderer.invoke('get-all-tags');
      setAllTags(allUpdatedTags);
      setNewTag('');
    }
  };

  const removeTag = async (tagName: string): Promise<void> => {
    if (selectedPhoto) {
      await window.electron.ipcRenderer.invoke('remove-tag-from-photo', { photoId: selectedPhoto.id, tagName });
      const updatedTags: string[] = await window.electron.ipcRenderer.invoke('get-photo-tags', selectedPhoto.id);
      setPhotoTags(updatedTags);
      const allUpdatedTags: string[] = await window.electron.ipcRenderer.invoke('get-all-tags');
      setAllTags(allUpdatedTags);
    }
  };

  const clearAllThumbnails = async (): Promise<void> => {
    if (!confirm('Are you sure you want to clear all thumbnail information? This will regenerate all thumbnails.')) {
      return;
    }

    try {
      const result = await window.electron.ipcRenderer.invoke('clear-all-thumbnails');
      if (result.success) {
        console.log('Thumbnails cleared successfully');
        // Reload photos to show placeholder state
        await loadPhotos(selectedFolder, searchTags);
      } else {
        setError('Failed to clear thumbnails: ' + result.message);
      }
    } catch (error) {
      console.error('Error clearing thumbnails:', error);
      setError('Failed to clear thumbnails. Please try again.');
    }
  };

  const handleSearch = (selectedOptions: string[]): void => {
    setSearchTags(selectedOptions);
    loadPhotos(selectedFolder, selectedOptions);
  };

  useEffect(() => {
    loadPhotos();
  }, [selectedFolder]);

  // Listen for real-time thumbnail updates
  useEffect(() => {
    const handleThumbnailGenerated = (data: { photoId: number; thumbnailPath: string }): void => {
      console.log('Thumbnail generated for photo:', data.photoId, data.thumbnailPath);
      setPhotos(prevPhotos =>
        prevPhotos.map(photo =>
          photo.id === data.photoId
            ? { ...photo, thumbnail_path: data.thumbnailPath }
            : photo
        )
      );
    };

    // Set up IPC listeners
    const removeGeneratedListener = window.electron.ipcRenderer.on('thumbnail-generated', handleThumbnailGenerated);

    return () => {
      removeGeneratedListener();
    };
  }, []);

  // Keyboard navigation for fullscreen viewer
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      if (isFullscreenViewer) {
        switch (event.key) {
          case 'Escape':
            closeFullscreenViewer();
            break;
          case 'ArrowLeft':
            navigatePhoto('prev');
            break;
          case 'ArrowRight':
            navigatePhoto('next');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreenViewer, currentPhotoIndex, photos]);

  return (
    <div className="flex h-screen bg-gradient-to-br from-slate-50 to-slate-100 text-slate-800 font-sans">
      {/* Modern Sidebar */}
      <aside className="w-72 bg-white/80 backdrop-blur-sm border-r border-slate-200 shadow-lg">
        <div className="p-6 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-white">
          <h2 className="text-xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
            Folders
          </h2>
        </div>
        <div className="p-4 overflow-y-auto h-[calc(100%-80px)]">
          {loadingState.scanning ? (
            <div className="flex items-center gap-3 text-slate-600 text-sm p-3 bg-blue-50 rounded-lg">
              <div className="w-4 h-4 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
              Scanning folder...
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-4 shadow-sm">
              <div className="flex items-start gap-2">
                <svg className="w-5 h-5 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div className="flex-1">
                  <p className="text-sm">{error}</p>
                  <button
                    className="text-xs text-red-600 hover:text-red-800 underline mt-1"
                    onClick={() => setError(null)}
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          ) : loadingState.folders ? (
            <div className="flex items-center gap-3 text-slate-600 text-sm p-3 bg-slate-50 rounded-lg">
              <div className="w-4 h-4 border-2 border-slate-400/30 border-t-slate-400 rounded-full animate-spin"></div>
              Loading tree...
            </div>
          ) : folderTree ? (
            <div className="space-y-1">
              {folderTree.map((node, index) => (
                <FolderTree
                  key={index}
                  node={node}
                  onSelect={handleFolderSelect}
                  selectedPath={selectedFolder}
                  isRoot={true}
                />
              ))}
            </div>
          ) : (
            <div className="text-slate-500 text-sm p-3 bg-slate-50 rounded-lg text-center">
              <svg className="w-8 h-8 mx-auto mb-2 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
              </svg>
              Select a folder to view tree
            </div>
          )}
        </div>
      </aside>

      {/* Modern Main Content */}
      <main className="flex-1 flex flex-col overflow-hidden">
        {/* Header Bar */}
        <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                Photo Gallery
              </h1>
              {selectedFolder && (
                <div className="flex items-center gap-2 text-sm text-slate-600 bg-slate-50 px-3 py-1.5 rounded-full">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                  </svg>
                  <span className="font-medium truncate max-w-md">{selectedFolder.split('\\').pop()}</span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-3">
              {selectedFolder && (
                <button
                  onClick={() => {
                    const parentPath = selectedFolder.split('\\').slice(0, -1).join('\\');
                    if (parentPath) {
                      handleFolderSelect(parentPath);
                    }
                  }}
                  className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium flex items-center gap-2"
                  title="Go to parent folder"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Parent Folder
                </button>
              )}
              <button
                onClick={selectFolder}
                disabled={loadingState.scanning}
                className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
              >
                {loadingState.scanning ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Scanning...
                  </div>
                ) : (
                  'Select Folder'
                )}
              </button>
              <button
                onClick={clearAllThumbnails}
                className="px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
                title="Clear all thumbnail information and regenerate them"
              >
                Clear Thumbnails
              </button>
            </div>
          </div>

          {/* Filters */}
          {allTags.length > 0 && (
            <div className="mt-4">
              <label className="text-sm font-medium text-slate-700 mb-3 block">Filter by tags:</label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto p-3 bg-white/60 rounded-lg border border-slate-200">
                {allTags.map((tag) => (
                  <label
                    key={tag}
                    className="flex items-center gap-2 px-3 py-1.5 bg-white rounded-full border border-slate-200 hover:border-slate-300 cursor-pointer transition-all text-sm"
                  >
                    <input
                      type="checkbox"
                      checked={searchTags.includes(tag)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          handleSearch([...searchTags, tag]);
                        } else {
                          handleSearch(searchTags.filter(t => t !== tag));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span className="text-slate-700">{tag}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </header>



        {/* Photo Grid */}
        <div className="flex-1 overflow-auto p-6">
          {loadingState.photos ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="w-8 h-8 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-slate-600">Loading photos...</p>
              </div>
            </div>
          ) : photos.length > 0 ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <p className="text-sm text-slate-600 font-medium">
                  Found <span className="text-slate-800 font-semibold">{photos.length}</span> photos
                </p>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-4">
                {photos.map((photo) => (
                  <div
                    key={photo.id}
                    className="group relative bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-slate-200/50 hover:border-slate-300 cursor-pointer"
                    onClick={() => openPhotoView(photo)}
                  >
                    <div className="aspect-square relative overflow-hidden">
                      {photo.thumbnail_path ? (
                        <img
                          src={`thumbnail://${photo.thumbnail_path}`}
                          alt={photo.path}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                          onLoad={() => {/* Thumbnail loaded successfully */}}
                          onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                            console.error('Failed to load thumbnail:', `thumbnail://${photo.thumbnail_path}`);
                            (e.target as HTMLImageElement).style.display = 'none';
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-slate-100 to-slate-200 flex flex-col items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500/30 border-t-blue-500 mb-2"></div>
                          <span className="text-xs text-slate-500 text-center px-2">Generating...</span>
                        </div>
                      )}

                      {/* Hover Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    {/* Photo Info */}
                    <div className="p-3">
                      <p className="text-xs text-slate-500 text-center">
                        {new Date(photo.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Load More Button */}
              {hasMorePhotos && !loadingState.photos && (
                <div className="flex justify-center mt-8">
                  <button
                    onClick={() => loadPhotos(selectedFolder || undefined, searchTags, false)}
                    disabled={loadingState.loadingMore}
                    className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                  >
                    {loadingState.loadingMore ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        Loading more...
                      </>
                    ) : (
                      <>
                        Load More Photos
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <svg className="w-16 h-16 mx-auto mb-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-slate-600 text-lg font-medium mb-2">No photos found</p>
                <p className="text-slate-500 text-sm">Select a folder to scan or search by tags</p>
              </div>
            </div>
          )}
        </div>

        {/* Modern Fullscreen Photo Viewer */}
        {isFullscreenViewer && selectedPhoto && (
          <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
            {/* Close Button */}
            <button
              onClick={closeFullscreenViewer}
              className="absolute top-4 right-4 z-60 w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Navigation Arrows */}
            {currentPhotoIndex > 0 && (
              <button
                onClick={() => navigatePhoto('prev')}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 z-60 w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
            )}

            {currentPhotoIndex < photos.length - 1 && (
              <button
                onClick={() => navigatePhoto('next')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 z-60 w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            )}

            {/* Photo Counter */}
            <div className="absolute top-4 left-4 z-60 bg-black/50 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
              {currentPhotoIndex + 1} / {photos.length}
            </div>

            {/* Main Photo */}
            <div className="absolute inset-0 flex items-center justify-center">
              <img
                src={`file://${selectedPhoto.path.replace(/\\/g, '/')}`}
                alt={selectedPhoto.path}
                className="w-full h-full object-contain"
                onLoad={() => {
                  console.log('Photo loaded successfully:', selectedPhoto.path);
                }}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                  console.error('Failed to load full image:', selectedPhoto.path);
                  console.error('Photo URL was:', `file://${selectedPhoto.path.replace(/\\/g, '/')}`);
                  // Fallback to thumbnail
                  if (selectedPhoto.thumbnail_path) {
                    (e.target as HTMLImageElement).src = `thumbnail://${selectedPhoto.thumbnail_path}`;
                  }
                }}
              />
            </div>

            {/* Info Panel */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 text-white">
              <div className="max-w-4xl mx-auto">
                <h3 className="text-lg font-semibold mb-2">{selectedPhoto.path.split('\\').pop()}</h3>
                <p className="text-sm text-white/80 mb-4">
                  {new Date(selectedPhoto.date).toLocaleDateString()} • {selectedPhoto.path}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {photoTags.map((tag) => (
                    <span
                      key={tag}
                      className="group bg-white/20 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm hover:bg-red-500/80 cursor-pointer transition-all flex items-center gap-2"
                      onClick={() => removeTag(tag)}
                    >
                      {tag}
                      <svg className="w-3 h-3 opacity-60 group-hover:opacity-100" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </span>
                  ))}
                </div>

                {/* Add Tag Controls */}
                <div className="space-y-3">
                  {/* Existing Tags */}
                  {allTags.filter((tag) => !photoTags.includes(tag)).length > 0 && (
                    <div>
                      <p className="text-white/80 text-sm mb-2">Add existing tag:</p>
                      <div className="flex flex-wrap gap-2">
                        {allTags.filter((tag) => !photoTags.includes(tag)).slice(0, 8).map((tag) => (
                          <button
                            key={tag}
                            onClick={() => {
                              setNewTag(tag);
                              addTag();
                            }}
                            className="bg-white/10 hover:bg-white/20 text-white px-3 py-1.5 rounded-full text-sm transition-all border border-white/20 hover:border-white/40"
                          >
                            + {tag}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Create New Tag */}
                  <div>
                    <p className="text-white/80 text-sm mb-2">Create new tag:</p>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={newTag}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewTag(e.target.value)}
                        placeholder="Enter tag name"
                        className="flex-1 bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-white/50"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter' && newTag.trim()) {
                            addTag();
                          }
                        }}
                      />
                      <button
                        onClick={addTag}
                        disabled={!newTag.trim()}
                        className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:opacity-50 text-white px-4 py-2 rounded-lg text-sm transition-all flex items-center gap-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Add
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default App;


