import React, { useState, useEffect } from 'react';

// Define interfaces with strict TypeScript standards
interface Photo {
  id: number;
  path: string;
  date: string;
  thumbnail_path?: string;
}

interface FolderNode {
  name: string;
  path: string;
  isDirectory: boolean;
  children?: FolderNode[];
}

interface ElectronAPI {
  ipcRenderer: {
    invoke(channel: 'select-folder'): Promise<string | null>;
    invoke(
      channel: 'get-photos',
      args: { offset: number; limit: number; tags: string[]; folderPath?: string }
    ): Promise<Photo[]>;
    invoke(channel: 'get-folder-tree', folderPath: string): Promise<FolderNode[] | null>;
    invoke(channel: 'get-photo-tags', photoId: number): Promise<string[]>;
    invoke(channel: 'get-all-tags'): Promise<string[]>;
    invoke(channel: 'add-tag-to-photo', args: { photoId: number; tagName: string }): Promise<void>;
    invoke(channel: 'remove-tag-from-photo', args: { photoId: number; tagName: string }): Promise<void>;
  };
}

declare global {
  interface Window {
    electron: ElectronAPI;
  }
}

// Typed FolderTree component
const FolderTree: React.FC<{ node: FolderNode; onSelect: (path: string) => void; isRoot?: boolean }> = ({
  node,
  onSelect,
  isRoot = false,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(isRoot);

  if (!node.isDirectory) return null;

  return (
    <div className="group">
      <div
        className="flex items-center cursor-pointer hover:bg-gray-100 p-2 rounded text-sm text-gray-700 hover:text-gray-900 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        {node.children && node.children.length > 0 && <span className="mr-1">{isOpen ? '▼' : '▶'}</span>}
        {node.name}
      </div>
      {isOpen && node.children && (
        <div className="ml-4">
          {node.children.map((child, index) => (
            <FolderTree key={index} node={child} onSelect={onSelect} />
          ))}
        </div>
      )}
      <button
        onClick={() => onSelect(node.path)}
        className="hidden group-hover:block ml-2 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
      >
        Open
      </button>
    </div>
  );
};

const App: React.FC = () => {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loadingState, setLoadingState] = useState<{
    photos: boolean;
    folders: boolean;
    tags: boolean;
    scanning: boolean;
  }>({
    photos: false,
    folders: false,
    tags: false,
    scanning: false
  });
  const [error, setError] = useState<string | null>(null);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [folderTree, setFolderTree] = useState<FolderNode[] | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [photoTags, setPhotoTags] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);
  const [searchTags, setSearchTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState<string>('');

  // Add pagination state
  const [pagination, setPagination] = useState({
    offset: 0,
    limit: 20,
    hasMore: true
  });

  const selectFolder = async (): Promise<void> => {
    try {
      setLoadingState(prev => ({ ...prev, scanning: true }));
      setError(null);
      
      const folderPath = await window.electron.ipcRenderer.invoke('select-folder');
      if (folderPath) {
        setSelectedFolder(folderPath);
        
        // Load folder tree
        setLoadingState(prev => ({ ...prev, folders: true }));
        const tree = await window.electron.ipcRenderer.invoke('get-folder-tree', folderPath);
        setFolderTree(tree);
        
        // Load photos
        setLoadingState(prev => ({ ...prev, photos: true }));
        await loadPhotos(folderPath, searchTags);
        
        // Load all tags
        setLoadingState(prev => ({ ...prev, tags: true }));
        const tags = await window.electron.ipcRenderer.invoke('get-all-tags');
        setAllTags(tags);
      }
    } catch (err) {
      console.error('Error selecting folder:', err);
      setError('Failed to scan folder. Please try again.');
    } finally {
      setLoadingState({
        photos: false,
        folders: false,
        tags: false,
        scanning: false
      });
    }
  };

  // Update loadPhotos function to handle pagination
  const loadPhotos = async (folderPath?: string, tags?: string[], reset: boolean = true): Promise<void> => {
    if (!folderPath && !selectedFolder) return;
    
    try {
      setLoadingState(prev => ({ ...prev, photos: true }));
      setError(null);
      
      const pathToUse: string = folderPath || selectedFolder!;
      const tagsToUse: string[] = tags || [];
      const offsetToUse = reset ? 0 : pagination.offset;
      
      const fetchedPhotos: Photo[] = await window.electron.ipcRenderer.invoke('get-photos', {
        offset: offsetToUse,
        limit: pagination.limit,
        tags: tagsToUse,
        folderPath: pathToUse,
      });
      
      setPagination(prev => ({
        ...prev,
        offset: reset ? pagination.limit : prev.offset + pagination.limit,
        hasMore: fetchedPhotos.length === pagination.limit
      }));
      
      setPhotos(prev => reset ? fetchedPhotos : [...prev, ...fetchedPhotos]);
    } catch (err) {
      console.error('Error loading photos:', err);
      setError('Failed to load photos. Please try again.');
    } finally {
      setLoadingState(prev => ({ ...prev, photos: false }));
    }
  };

  const handleFolderSelect = async (path: string): Promise<void> => {
    setSelectedFolder(path);
    await loadPhotos(path, searchTags);
  };

  const openPhotoView = async (photo: Photo): Promise<void> => {
    setSelectedPhoto(photo);
    const tags: string[] = await window.electron.ipcRenderer.invoke('get-photo-tags', photo.id);
    setPhotoTags(tags);
    setNewTag('');
  };

  const addTag = async (): Promise<void> => {
    if (newTag.trim() && selectedPhoto) {
      await window.electron.ipcRenderer.invoke('add-tag-to-photo', { photoId: selectedPhoto.id, tagName: newTag.trim() });
      const updatedTags: string[] = await window.electron.ipcRenderer.invoke('get-photo-tags', selectedPhoto.id);
      setPhotoTags(updatedTags);
      const allUpdatedTags: string[] = await window.electron.ipcRenderer.invoke('get-all-tags');
      setAllTags(allUpdatedTags);
      setNewTag('');
    }
  };

  const removeTag = async (tagName: string): Promise<void> => {
    if (selectedPhoto) {
      await window.electron.ipcRenderer.invoke('remove-tag-from-photo', { photoId: selectedPhoto.id, tagName });
      const updatedTags: string[] = await window.electron.ipcRenderer.invoke('get-photo-tags', selectedPhoto.id);
      setPhotoTags(updatedTags);
      const allUpdatedTags: string[] = await window.electron.ipcRenderer.invoke('get-all-tags');
      setAllTags(allUpdatedTags);
    }
  };

  const handleSearch = (selectedOptions: string[]): void => {
    setSearchTags(selectedOptions);
    loadPhotos(selectedFolder, selectedOptions);
  };

  useEffect(() => {
    loadPhotos();
  }, [selectedFolder]);

  return (
    <div className="flex h-screen bg-gray-50 text-gray-800 font-segoe">
      <aside className="w-64 bg-white border-r border-gray-200 shadow-sm">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Folders</h2>
        </div>
        <div className="p-2 overflow-y-auto h-[calc(100%-64px)]">
          {loadingState.scanning ? (
            <p className="text-gray-500 text-sm p-2">Scanning folder...</p>
          ) : error ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <p>{error}</p>
              <button 
                className="text-sm underline ml-2" 
                onClick={() => setError(null)}
              >
                Dismiss
              </button>
            </div>
          ) : loadingState.folders ? (
            <p className="text-gray-500 text-sm p-2">Loading tree...</p>
          ) : folderTree ? (
            folderTree.map((node, index) => (
              <FolderTree key={index} node={node} onSelect={handleFolderSelect} isRoot={true} />
            ))
          ) : (
            <p className="text-gray-500 text-sm p-2">Select a folder to view tree.</p>
          )}
        </div>
      </aside>

      <main className="flex-1 p-4 overflow-auto">
        <div className="mb-4 flex items-center space-x-4">
          <button
            onClick={selectFolder}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
            disabled={loadingState.scanning}
          >
            {loadingState.scanning ? 'Scanning...' : 'Select Folder'}
          </button>
          {selectedFolder && <p className="text-sm text-gray-700">Selected: {selectedFolder}</p>}
          <select
            multiple
            value={searchTags}
            onChange={(e) => handleSearch(Array.from(e.target.selectedOptions).map((option) => option.value))}
            className="p-2 border border-gray-300 rounded w-64 h-24 overflow-y-auto"
          >
            {allTags.map((tag) => (
              <option key={tag} value={tag}>
                {tag}
              </option>
            ))}
          </select>
        </div>
        {loadingState.photos ? (
          <p className="text-center text-gray-500">Loading photos...</p>
        ) : photos.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {photos.map((photo) => (
              <div
                key={photo.id}
                className="bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow flex flex-col items-center justify-center border border-gray-100"
              >
                {photo.thumbnail_path ? (
                  <img
                    src={`thumbnail://${photo.thumbnail_path}`}
                    alt={photo.path}
                    className="w-full h-32 object-cover rounded"
                    onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                      console.error('Failed to load thumbnail:', `thumbnail://${photo.thumbnail_path}`);
                      (e.target as HTMLImageElement).style.display = 'none'; // Hide broken image
                    }}
                  />
                ) : (
                  <div className="w-full h-32 bg-gray-200 flex items-center justify-center text-gray-600 text-sm break-all">
                    {photo.path}
                  </div>
                )}
                <p className="mt-2 text-xs text-gray-500">{new Date(photo.date).toLocaleDateString()}</p>
                <button
                  onClick={() => openPhotoView(photo)}
                  className="mt-2 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                >
                  View Photo
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500">No photos found. Select a folder to scan or search by tags.</p>
        )}

        {selectedPhoto && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg w-[500px]">
              <h3 className="text-lg font-semibold mb-4">Photo Details</h3>
              <div className="mb-4">
                {selectedPhoto.thumbnail_path && (
                  <img
                    src={`thumbnail://${selectedPhoto.thumbnail_path}`}
                    alt={selectedPhoto.path}
                    className="w-full h-48 object-cover rounded mb-2"
                    onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                      console.error('Failed to load thumbnail in modal:', `thumbnail://${selectedPhoto.thumbnail_path}`);
                      (e.target as HTMLImageElement).style.display = 'none'; // Hide broken image
                    }}
                  />
                )}
                <p className="text-sm text-gray-700">Path: {selectedPhoto.path}</p>
                <p className="text-sm text-gray-700">Date: {new Date(selectedPhoto.date).toLocaleDateString()}</p>
              </div>
              <div className="mb-4">
                <h4 className="text-md font-medium mb-2">Tags</h4>
                <div className="mb-2">
                  {photoTags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-block bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-2 hover:bg-gray-300 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    >
                      {tag} ×
                    </span>
                  ))}
                </div>
                <div className="flex flex-col gap-2">
                  <div className="flex gap-2">
                    <select
                      value={newTag}
                      onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setNewTag(e.target.value)}
                      className="flex-1 p-2 border border-gray-300 rounded"
                    >
                      <option value="">Select existing tag...</option>
                      {allTags.filter((tag) => !photoTags.includes(tag)).map((tag) => (
                        <option key={tag} value={tag}>
                          {tag}
                        </option>
                      ))}
                    </select>
                    <button
                      onClick={addTag}
                      disabled={!newTag.trim()}
                      className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400"
                    >
                      Add
                    </button>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">Or create new:</span>
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewTag(e.target.value)}
                      placeholder="Enter new tag"
                      className="flex-1 p-2 border border-gray-300 rounded"
                    />
                  </div>
                </div>
              </div>
              <button
                onClick={() => setSelectedPhoto(null)}
                className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Close
              </button>
            </div>
          </div>
        )}
        {pagination.hasMore && (
          <div className="text-center mt-4">
            <button
              onClick={() => loadPhotos(selectedFolder, searchTags, false)}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
              disabled={loadingState.photos}
            >
              {loadingState.photos ? 'Loading...' : 'Load More'}
            </button>
          </div>
        )}
      </main>
    </div>
  );
};

export default App;


